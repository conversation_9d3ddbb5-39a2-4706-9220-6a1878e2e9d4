"""
管理端API接口
提供管理员认证、用户管理、平台管理、任务管理等功能
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Optional, List, Dict, Any
from datetime import datetime
import logging

from database import get_db_session
from models import Admin, User, Platform, Task, Cookie, Settlement, InviteCode, AdminRole
from models.user import UserStatus
from models.cookie import CookieStatus
from models.platform import PlatformStatus
from middleware.admin_auth import get_current_admin, admin_required, super_admin_required
from utils.auth import authenticate_admin, create_admin_token
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)
router = APIRouter()


# ==================== Pydantic 模型定义 ====================

class AdminLoginRequest(BaseModel):
    """管理员登录请求模型"""
    username: str = Field(..., description="管理员用户名")
    password: str = Field(..., description="密码")


class AdminLoginResponse(BaseModel):
    """管理员登录响应模型"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    admin_info: Dict[str, Any] = Field(..., description="管理员信息")


class AdminInfoResponse(BaseModel):
    """管理员信息响应模型"""
    id: int = Field(..., description="管理员ID")
    username: str = Field(..., description="用户名")
    role: str = Field(..., description="角色")
    created_at: datetime = Field(..., description="创建时间")


class UserListResponse(BaseModel):
    """用户列表响应模型"""
    id: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    alipay: Optional[str] = Field(None, description="支付宝结款手机号")
    status: str = Field(..., description="用户状态")
    referrer_id: Optional[int] = Field(None, description="推荐人ID")
    referrer_username: Optional[str] = Field(None, description="推荐人用户名")
    created_at: datetime = Field(..., description="注册时间")
    task_count: int = Field(default=0, description="完成任务数")
    total_earnings: float = Field(default=0.0, description="总收益")


class UserDetailResponse(BaseModel):
    """用户详情响应模型"""
    id: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    alipay: Optional[str] = Field(None, description="支付宝结款手机号")
    status: str = Field(..., description="用户状态")
    referrer_id: Optional[int] = Field(None, description="推荐人ID")
    referrer_username: Optional[str] = Field(None, description="推荐人用户名")
    created_at: datetime = Field(..., description="注册时间")
    updated_at: datetime = Field(..., description="更新时间")
    task_count: int = Field(default=0, description="完成任务数")
    total_earnings: float = Field(default=0.0, description="总收益")
    referral_count: int = Field(default=0, description="推荐用户数")
    invite_codes: List[Dict[str, Any]] = Field(default=[], description="邀请码列表")


class UpdateUserStatusRequest(BaseModel):
    """修改用户状态请求模型"""
    status: str = Field(..., description="用户状态", pattern="^(active|disabled)$")


class InviteCodeListResponse(BaseModel):
    """邀请码列表响应模型"""
    id: int = Field(..., description="邀请码ID")
    code: str = Field(..., description="邀请码")
    creator_id: int = Field(..., description="创建者ID")
    creator_username: str = Field(..., description="创建者用户名")
    used_by: Optional[int] = Field(None, description="使用者ID")
    used_by_username: Optional[str] = Field(None, description="使用者用户名")
    used_at: Optional[datetime] = Field(None, description="使用时间")
    created_at: datetime = Field(..., description="创建时间")


class PaginationResponse(BaseModel):
    """分页响应模型"""
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")
    data: List[Any] = Field(..., description="数据列表")


class PlatformListResponse(BaseModel):
    """平台列表响应模型"""
    id: int = Field(..., description="平台ID")
    code: str = Field(..., description="平台代码")
    name: str = Field(..., description="平台名称")
    base_price: float = Field(..., description="任务基准价格")
    commission_rate: float = Field(..., description="邀请佣金比例")
    status: str = Field(..., description="平台状态")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    task_count: int = Field(default=0, description="任务总数")
    active_task_count: int = Field(default=0, description="活跃任务数")


class CreatePlatformRequest(BaseModel):
    """创建平台请求模型"""
    code: str = Field(..., description="平台代码", pattern="^[a-z0-9_]+$", max_length=50)
    name: str = Field(..., description="平台名称", max_length=100)
    base_price: float = Field(..., description="任务基准价格", ge=0.01, le=999.99)
    commission_rate: float = Field(..., description="邀请佣金比例", ge=0.0, le=1.0)


class UpdatePlatformRequest(BaseModel):
    """更新平台请求模型"""
    name: Optional[str] = Field(None, description="平台名称", max_length=100)
    base_price: Optional[float] = Field(None, description="任务基准价格", ge=0.01, le=999.99)
    commission_rate: Optional[float] = Field(None, description="邀请佣金比例", ge=0.0, le=1.0)
    status: Optional[str] = Field(None, description="平台状态", pattern="^(active|disabled)$")


# ==================== 管理员认证接口 ====================

@router.post("/login", response_model=AdminLoginResponse, summary="管理员登录")
async def admin_login(
    request: AdminLoginRequest,
    db: Session = Depends(get_db_session)
):
    """
    ## 管理员登录接口
    
    验证管理员用户名和密码，返回JWT访问令牌。
    
    **请求参数:**
    - username: 管理员用户名
    - password: 密码
    
    **响应示例:**
    ```json
    {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "token_type": "bearer",
        "admin_info": {
            "id": 1,
            "username": "admin",
            "role": "super_admin",
            "created_at": "2024-01-01T12:00:00Z"
        }
    }
    ```
    
    **错误响应:**
    - 401: 用户名或密码错误
    """
    try:
        # 验证管理员凭据
        admin = authenticate_admin(request.username, request.password, db)
        if not admin:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        # 生成访问令牌
        access_token = create_admin_token(admin)
        
        # 构建响应
        admin_info = {
            "id": admin.id,
            "username": admin.username,
            "role": admin.role.value,
            "created_at": admin.created_at.isoformat()
        }
        
        logger.info(f"管理员 {admin.username} 登录成功")
        
        return AdminLoginResponse(
            access_token=access_token,
            token_type="bearer",
            admin_info=admin_info
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"管理员登录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录失败，请稍后重试"
        )


@router.post("/logout", summary="管理员登出")
async def admin_logout(
    current_admin: Admin = Depends(admin_required)
):
    """
    ## 管理员登出接口
    
    管理员登出（客户端需要删除本地存储的token）。
    
    **响应示例:**
    ```json
    {
        "message": "登出成功"
    }
    ```
    """
    logger.info(f"管理员 {current_admin.username} 登出")
    return {"message": "登出成功"}


@router.get("/info", response_model=AdminInfoResponse, summary="获取当前管理员信息")
async def get_admin_info(
    current_admin: Admin = Depends(admin_required)
):
    """
    ## 获取当前管理员信息
    
    返回当前登录管理员的基本信息。
    
    **响应示例:**
    ```json
    {
        "id": 1,
        "username": "admin",
        "role": "super_admin",
        "created_at": "2024-01-01T12:00:00Z"
    }
    ```
    """
    return AdminInfoResponse(
        id=current_admin.id,
        username=current_admin.username,
        role=current_admin.role.value,
        created_at=current_admin.created_at
    )


# ==================== 系统健康检查 ====================

@router.get("/health", summary="管理端健康检查")
async def admin_health_check():
    """
    ## 管理端健康检查接口
    
    用于检查管理端API运行状态。
    
    **响应示例:**
    ```json
    {
        "status": "healthy",
        "service": "admin-api",
        "timestamp": "2024-01-01T12:00:00Z"
    }
    ```
    """
    return {
        "status": "healthy",
        "service": "admin-api",
        "timestamp": datetime.now().isoformat()
    }


# ==================== 用户管理接口 ====================

@router.get("/users", response_model=PaginationResponse, summary="获取用户列表")
async def get_users(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    search: Optional[str] = Query(None, description="搜索用户名"),
    status: Optional[str] = Query(None, description="用户状态筛选", pattern="^(active|disabled)$"),
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db_session)
):
    """
    ## 获取用户列表

    支持分页、搜索和状态筛选的用户列表查询。

    **查询参数:**
    - page: 页码，从1开始
    - size: 每页大小，1-100
    - search: 搜索用户名（模糊匹配）
    - status: 用户状态筛选（active/disabled）

    **响应示例:**
    ```json
    {
        "total": 100,
        "page": 1,
        "size": 20,
        "pages": 5,
        "data": [
            {
                "id": 1,
                "username": "user1",
                "alipay": "13800138000",
                "status": "active",
                "referrer_id": 2,
                "referrer_username": "user2",
                "created_at": "2024-01-01T12:00:00Z",
                "task_count": 10,
                "total_earnings": 50.0
            }
        ]
    }
    ```
    """
    try:
        # 构建查询
        query = db.query(User)

        # 搜索条件
        if search:
            query = query.filter(User.username.contains(search))

        # 状态筛选
        if status:
            query = query.filter(User.status == UserStatus(status))

        # 获取总数
        total = query.count()

        # 分页
        offset = (page - 1) * size
        users = query.offset(offset).limit(size).all()

        # 构建响应数据
        user_list = []
        for user in users:
            # 获取推荐人信息
            referrer_username = None
            if user.referrer_id:
                referrer = db.query(User).filter(User.id == user.referrer_id).first()
                if referrer:
                    referrer_username = referrer.username

            # 获取任务统计（简化版，实际应该调用CommissionService）
            task_count = db.query(Task).filter(
                Task.assigned_to == user.id,
                Task.status == 'completed'
            ).count()

            user_data = UserListResponse(
                id=user.id,
                username=user.username,
                alipay=user.alipay,
                status=user.status.value,
                referrer_id=user.referrer_id,
                referrer_username=referrer_username,
                created_at=user.created_at,
                task_count=task_count,
                total_earnings=0.0  # 简化处理，实际应该计算真实收益
            )
            user_list.append(user_data)

        # 计算总页数
        pages = (total + size - 1) // size

        return PaginationResponse(
            total=total,
            page=page,
            size=size,
            pages=pages,
            data=user_list
        )

    except Exception as e:
        logger.error(f"获取用户列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户列表失败"
        )


@router.get("/users/{user_id}", response_model=UserDetailResponse, summary="获取用户详情")
async def get_user_detail(
    user_id: int,
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db_session)
):
    """
    ## 获取用户详情

    获取指定用户的详细信息，包括任务统计、收益信息、推荐关系等。

    **响应示例:**
    ```json
    {
        "id": 1,
        "username": "user1",
        "alipay": "13800138000",
        "status": "active",
        "referrer_id": 2,
        "referrer_username": "user2",
        "created_at": "2024-01-01T12:00:00Z",
        "updated_at": "2024-01-01T12:00:00Z",
        "task_count": 10,
        "total_earnings": 50.0,
        "referral_count": 5,
        "invite_codes": [
            {
                "id": 1,
                "code": "ABC123",
                "used_by": 3,
                "used_by_username": "user3",
                "used_at": "2024-01-01T12:00:00Z"
            }
        ]
    }
    ```
    """
    try:
        # 查找用户
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )

        # 获取推荐人信息
        referrer_username = None
        if user.referrer_id:
            referrer = db.query(User).filter(User.id == user.referrer_id).first()
            if referrer:
                referrer_username = referrer.username

        # 获取任务统计
        task_count = db.query(Task).filter(
            Task.assigned_to == user.id,
            Task.status == 'completed'
        ).count()

        # 获取推荐用户数
        referral_count = db.query(User).filter(User.referrer_id == user.id).count()

        # 获取邀请码信息
        invite_codes_query = db.query(InviteCode).filter(InviteCode.user_id == user.id).all()
        invite_codes = []
        for invite_code in invite_codes_query:
            used_by_username = None
            if invite_code.used_by:
                used_by_user = db.query(User).filter(User.id == invite_code.used_by).first()
                if used_by_user:
                    used_by_username = used_by_user.username

            invite_codes.append({
                "id": invite_code.id,
                "code": invite_code.code,
                "used_by": invite_code.used_by,
                "used_by_username": used_by_username,
                "used_at": invite_code.used_at.isoformat() if invite_code.used_at else None,
                "created_at": invite_code.created_at.isoformat()
            })

        return UserDetailResponse(
            id=user.id,
            username=user.username,
            alipay=user.alipay,
            status=user.status.value,
            referrer_id=user.referrer_id,
            referrer_username=referrer_username,
            created_at=user.created_at,
            updated_at=user.updated_at,
            task_count=task_count,
            total_earnings=0.0,  # 简化处理，实际应该计算真实收益
            referral_count=referral_count,
            invite_codes=invite_codes
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户详情失败"
        )


@router.put("/users/{user_id}/status", summary="修改用户状态")
async def update_user_status(
    user_id: int,
    request: UpdateUserStatusRequest,
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db_session)
):
    """
    ## 修改用户状态

    启用或禁用指定用户账户。

    **请求参数:**
    - status: 用户状态（active/disabled）

    **响应示例:**
    ```json
    {
        "message": "用户状态修改成功",
        "user_id": 1,
        "new_status": "disabled"
    }
    ```
    """
    try:
        # 查找用户
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )

        # 更新用户状态
        old_status = user.status.value
        user.status = UserStatus(request.status)
        user.updated_at = datetime.now()

        db.commit()

        logger.info(f"管理员 {current_admin.username} 将用户 {user.username} 状态从 {old_status} 修改为 {request.status}")

        return {
            "message": "用户状态修改成功",
            "user_id": user_id,
            "new_status": request.status
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"修改用户状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="修改用户状态失败"
        )


@router.get("/invites", response_model=PaginationResponse, summary="获取邀请码列表")
async def get_invite_codes(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    used: Optional[bool] = Query(None, description="是否已使用筛选"),
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db_session)
):
    """
    ## 获取邀请码列表

    查看所有邀请码的使用情况，支持分页和使用状态筛选。

    **查询参数:**
    - page: 页码，从1开始
    - size: 每页大小，1-100
    - used: 是否已使用筛选（true/false）

    **响应示例:**
    ```json
    {
        "total": 50,
        "page": 1,
        "size": 20,
        "pages": 3,
        "data": [
            {
                "id": 1,
                "code": "ABC123",
                "creator_id": 1,
                "creator_username": "user1",
                "used_by": 2,
                "used_by_username": "user2",
                "used_at": "2024-01-01T12:00:00Z",
                "created_at": "2024-01-01T10:00:00Z"
            }
        ]
    }
    ```
    """
    try:
        # 构建查询
        query = db.query(InviteCode)

        # 使用状态筛选
        if used is not None:
            if used:
                query = query.filter(InviteCode.used_by.isnot(None))
            else:
                query = query.filter(InviteCode.used_by.is_(None))

        # 获取总数
        total = query.count()

        # 分页
        offset = (page - 1) * size
        invite_codes = query.offset(offset).limit(size).all()

        # 构建响应数据
        invite_code_list = []
        for invite_code in invite_codes:
            # 获取创建者信息
            creator = db.query(User).filter(User.id == invite_code.user_id).first()
            creator_username = creator.username if creator else "未知"

            # 获取使用者信息
            used_by_username = None
            if invite_code.used_by:
                used_by_user = db.query(User).filter(User.id == invite_code.used_by).first()
                if used_by_user:
                    used_by_username = used_by_user.username

            invite_code_data = InviteCodeListResponse(
                id=invite_code.id,
                code=invite_code.code,
                creator_id=invite_code.user_id,
                creator_username=creator_username,
                used_by=invite_code.used_by,
                used_by_username=used_by_username,
                used_at=invite_code.used_at,
                created_at=invite_code.created_at
            )
            invite_code_list.append(invite_code_data)

        # 计算总页数
        pages = (total + size - 1) // size

        return PaginationResponse(
            total=total,
            page=page,
            size=size,
            pages=pages,
            data=invite_code_list
        )

    except Exception as e:
        logger.error(f"获取邀请码列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取邀请码列表失败"
        )


# ==================== 平台管理接口 ====================

@router.get("/platforms", response_model=PaginationResponse, summary="获取平台列表")
async def get_platforms(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    status: Optional[str] = Query(None, description="平台状态筛选", pattern="^(active|disabled)$"),
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db_session)
):
    """
    ## 获取平台列表

    支持分页和状态筛选的平台列表查询。

    **查询参数:**
    - page: 页码，从1开始
    - size: 每页大小，1-100
    - status: 平台状态筛选（active/disabled）

    **响应示例:**
    ```json
    {
        "total": 3,
        "page": 1,
        "size": 20,
        "pages": 1,
        "data": [
            {
                "id": 1,
                "code": "tb",
                "name": "淘宝",
                "base_price": 1.0,
                "commission_rate": 0.1,
                "status": "active",
                "created_at": "2024-01-01T12:00:00Z",
                "updated_at": "2024-01-01T12:00:00Z",
                "task_count": 100,
                "active_task_count": 20
            }
        ]
    }
    ```
    """
    try:
        # 构建查询
        query = db.query(Platform)

        # 状态筛选
        if status:
            query = query.filter(Platform.status == PlatformStatus(status))

        # 获取总数
        total = query.count()

        # 分页
        offset = (page - 1) * size
        platforms = query.offset(offset).limit(size).all()

        # 构建响应数据
        platform_list = []
        for platform in platforms:
            # 获取任务统计
            task_count = db.query(Task).filter(Task.platform_id == platform.id).count()
            active_task_count = db.query(Task).filter(
                Task.platform_id == platform.id,
                Task.status.in_(['pending', 'assigned', 'in_progress'])
            ).count()

            platform_data = PlatformListResponse(
                id=platform.id,
                code=platform.code,
                name=platform.name,
                base_price=float(platform.base_price),
                commission_rate=float(platform.commission_rate),
                status=platform.status.value,
                created_at=platform.created_at,
                updated_at=platform.updated_at,
                task_count=task_count,
                active_task_count=active_task_count
            )
            platform_list.append(platform_data)

        # 计算总页数
        pages = (total + size - 1) // size

        return PaginationResponse(
            total=total,
            page=page,
            size=size,
            pages=pages,
            data=platform_list
        )

    except Exception as e:
        logger.error(f"获取平台列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取平台列表失败"
        )


@router.post("/platforms", response_model=PlatformListResponse, summary="创建新平台")
async def create_platform(
    request: CreatePlatformRequest,
    current_admin: Admin = Depends(super_admin_required),
    db: Session = Depends(get_db_session)
):
    """
    ## 创建新平台

    创建新的电商平台配置。需要超级管理员权限。

    **请求参数:**
    - code: 平台代码（唯一，小写字母数字下划线）
    - name: 平台名称
    - base_price: 任务基准价格
    - commission_rate: 邀请佣金比例（0-1之间）

    **响应示例:**
    ```json
    {
        "id": 4,
        "code": "xhs",
        "name": "小红书",
        "base_price": 0.5,
        "commission_rate": 0.05,
        "status": "active",
        "created_at": "2024-01-01T12:00:00Z",
        "updated_at": "2024-01-01T12:00:00Z",
        "task_count": 0,
        "active_task_count": 0
    }
    ```
    """
    try:
        # 检查平台代码是否已存在
        existing_platform = db.query(Platform).filter(Platform.code == request.code).first()
        if existing_platform:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="平台代码已存在"
            )

        # 创建新平台
        new_platform = Platform(
            code=request.code,
            name=request.name,
            base_price=request.base_price,
            commission_rate=request.commission_rate,
            status=PlatformStatus.active
        )

        db.add(new_platform)
        db.commit()
        db.refresh(new_platform)

        logger.info(f"管理员 {current_admin.username} 创建了新平台: {request.code}")

        return PlatformListResponse(
            id=new_platform.id,
            code=new_platform.code,
            name=new_platform.name,
            base_price=float(new_platform.base_price),
            commission_rate=float(new_platform.commission_rate),
            status=new_platform.status.value,
            created_at=new_platform.created_at,
            updated_at=new_platform.updated_at,
            task_count=0,
            active_task_count=0
        )

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"创建平台失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建平台失败"
        )


@router.put("/platforms/{platform_id}", response_model=PlatformListResponse, summary="更新平台信息")
async def update_platform(
    platform_id: int,
    request: UpdatePlatformRequest,
    current_admin: Admin = Depends(admin_required),
    db: Session = Depends(get_db_session)
):
    """
    ## 更新平台信息

    修改平台的名称、价格、佣金比例或状态。

    **请求参数:**
    - name: 平台名称（可选）
    - base_price: 任务基准价格（可选）
    - commission_rate: 邀请佣金比例（可选）
    - status: 平台状态（可选，active/disabled）

    **响应示例:**
    ```json
    {
        "id": 1,
        "code": "tb",
        "name": "淘宝",
        "base_price": 1.2,
        "commission_rate": 0.12,
        "status": "active",
        "created_at": "2024-01-01T12:00:00Z",
        "updated_at": "2024-01-01T13:00:00Z",
        "task_count": 100,
        "active_task_count": 20
    }
    ```
    """
    try:
        # 查找平台
        platform = db.query(Platform).filter(Platform.id == platform_id).first()
        if not platform:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="平台不存在"
            )

        # 更新字段
        updated_fields = []
        if request.name is not None:
            platform.name = request.name
            updated_fields.append(f"name: {request.name}")

        if request.base_price is not None:
            platform.base_price = request.base_price
            updated_fields.append(f"base_price: {request.base_price}")

        if request.commission_rate is not None:
            platform.commission_rate = request.commission_rate
            updated_fields.append(f"commission_rate: {request.commission_rate}")

        if request.status is not None:
            platform.status = PlatformStatus(request.status)
            updated_fields.append(f"status: {request.status}")

        platform.updated_at = datetime.now()

        db.commit()

        # 获取任务统计
        task_count = db.query(Task).filter(Task.platform_id == platform.id).count()
        active_task_count = db.query(Task).filter(
            Task.platform_id == platform.id,
            Task.status.in_(['pending', 'assigned', 'in_progress'])
        ).count()

        logger.info(f"管理员 {current_admin.username} 更新了平台 {platform.code}: {', '.join(updated_fields)}")

        return PlatformListResponse(
            id=platform.id,
            code=platform.code,
            name=platform.name,
            base_price=float(platform.base_price),
            commission_rate=float(platform.commission_rate),
            status=platform.status.value,
            created_at=platform.created_at,
            updated_at=platform.updated_at,
            task_count=task_count,
            active_task_count=active_task_count
        )

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"更新平台失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新平台失败"
        )


@router.delete("/platforms/{platform_id}", summary="删除平台")
async def delete_platform(
    platform_id: int,
    current_admin: Admin = Depends(super_admin_required),
    db: Session = Depends(get_db_session)
):
    """
    ## 删除平台

    删除指定平台。需要超级管理员权限。

    **注意:** 只有没有关联任务和Cookie的平台才能被删除。

    **响应示例:**
    ```json
    {
        "message": "平台删除成功",
        "platform_id": 4,
        "platform_code": "xhs"
    }
    ```
    """
    try:
        # 查找平台
        platform = db.query(Platform).filter(Platform.id == platform_id).first()
        if not platform:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="平台不存在"
            )

        # 检查是否有关联的任务
        task_count = db.query(Task).filter(Task.platform_id == platform_id).count()
        if task_count > 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无法删除平台，存在 {task_count} 个关联任务"
            )

        # 检查是否有关联的Cookie
        cookie_count = db.query(Cookie).filter(Cookie.platform_id == platform_id).count()
        if cookie_count > 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无法删除平台，存在 {cookie_count} 个关联Cookie"
            )

        platform_code = platform.code
        db.delete(platform)
        db.commit()

        logger.info(f"管理员 {current_admin.username} 删除了平台: {platform_code}")

        return {
            "message": "平台删除成功",
            "platform_id": platform_id,
            "platform_code": platform_code
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"删除平台失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除平台失败"
        )
